from onexbots.shared.services.virtual_staff_service import VirtualStaff
from onexbots.shared.services.knowledge_service import KnowledgeService
from onexbots.shared.services.embedding_service import EmbeddingService
from ...tools.registry import ToolManager
from langchain.tools.retriever import create_retriever_tool
from langchain_core.tools import BaseTool
import logging
from typing import List

logger = logging.getLogger(__name__)

# Add this at the top-level, after logger definition
DEFAULT_TOOLS = ["timer"]


def get_tools_for_staff(staff: VirtualStaff) -> List[BaseTool]:
    """Get tools based on staff configuration."""
    tools_list: List[BaseTool] = []
    tool_manager = ToolManager()
    # Get enabled tools from staff configuration
    enabled_tools = staff["configuration"].get("tools", {}).get("enabled", [])
    if enabled_tools is None:
        enabled_tools = []
    tool_configs = staff["configuration"].get("tools", {}).get("config", {})
    # Always include default tools
    enabled_tools = list(set(enabled_tools + DEFAULT_TOOLS))
    if not enabled_tools:
        return []
    for tool_name in enabled_tools:
        try:
            # Get tool configuration
            tool_config = None
            if tool_configs:
                tool_config = tool_configs.get(tool_name, {})
            # Get tool instance
            tool = tool_manager.get_tool(tool_name)
            if tool:
                # Configure tool if needed
                if tool_config:
                    # Update tool configuration
                    for key, value in tool_config.items():
                        if hasattr(tool, key):
                            setattr(tool, key, value)

                tools_list.append(tool)
                logger.debug(f"Added tool: {tool_name}")
            else:
                logger.warning(f"Tool not found: {tool_name}")

        except Exception as e:
            logger.error(f"Error loading tool {tool_name}: {str(e)}")

    logger.info(f"Loaded {len(tools_list)} tools for staff {staff['name']}")
    return tools_list


# async def test_add_or_update_knowledge(knowledge_id: str):
#     """Test adding or updating knowledge document."""
#     embedding_service = EmbeddingService(settings)
#     knowledge_service = KnowledgeService()
#     knowledge = knowledge_service.get(knowledge_id)
#     content = """
# Các yêu cầu từ Doanh nghiệp len sợi & OneXBots

# 100%
# E10:E12

# - Thông tin công ty:
# - Công ty TNHH NoLi Handmade - NOLI HANDMADE COMPANY LIMITED
# - Hotline: **********
# - Email: <EMAIL>

# - Các kênh online của NoLi:
# 🌸Website: https://shop.noli.vn
# 🌼Shopee NoLi HN: https://shopee.vn/nolihandmadehn
# 🌼Shopee NoLi HCM: https://shopee.vn/nolihandmadehochiminh
# ❤️Lazada: https://lzd.co/nolihandmade
# ⭐️Tiki: https://tiki.vn/cua-hang/noli-handmade
# 🌳Tiktok NoLi: https://www.tiktok.com/@nolihandmade

# - Mua trực tiếp:
# NoLi Handmade HN
# 🏡 Số 41 ngõ 63 Phố Ô Đồng Lầm, đường ven Hồ Ba Mẫu, Phương Liên, Đống Đa, Hà Nội
# ⏰ Mở cửa: từ 8h-21h. Từ T2 đến Chủ nhật
# ☎️ Hotline: ********** (zalo)
# 🧶 Chỉ đường tới cửa hàng NoLi: https://maps.app.goo.gl/HmtRz7XXnzVCeP6KA

# - Mua trực tiếp:
# NoLi Handmade HCM
# 🏡Số M4-21 đường M4, khu Manhattan Glory, Vinhomes Grandpark, Long Bình, Thủ Đức
# ⏰Mở cửa: 9h-17h. Từ T2-T7
# ☎️ Hotline: ********** (zalo)
# 🧶 Chỉ đường tới: https://maps.app.goo.gl/aKPmoNWzfXsZ8ddN7

# - STK:
# Chủ tài khoản: Mai Khánh Linh
# *MBBank: **********
# * Momo: **********

# ❤️ Học đan móc online dễ dàng cùng NoLi:
# Website: https://noli.vn
# Youtube: https://www.youtube.com/@NoLiHandmadeChannel
# Group: https://www.facebook.com/groups/lopmocbalolennhung

# - Danh sách sản phẩm và mô tả sản phẩm: https://docs.google.com/spreadsheets/d/1JR4UOXeCUG7I2DVscu-YOtgLGEc9qpSl/edit?usp=sharing&ouid=114889868491849224087&rtpof=true&sd=true
# - Video hướng dẫn: https://youtu.be/zjVD1kbNo2M?si=Qn7lzj4g9FhjjECW
# - Thông tin công ty:
# - Công ty TNHH NoLi Handmade - NOLI HANDMADE COMPANY LIMITED
# - Hotline: **********
# - Email: <EMAIL>

# - Các kênh online của NoLi:
# 🌸Website: https://shop.noli.vn
# 🌼Shopee NoLi HN: https://shopee.vn/nolihandmadehn
# 🌼Shopee NoLi HCM: https://shopee.vn/nolihandmadehochiminh
# ❤️Lazada: https://lzd.co/nolihandmade
# ⭐️Tiki: https://tiki.vn/cua-hang/noli-handmade
# 🌳Tiktok NoLi: https://www.tiktok.com/@nolihandmade

# - Mua trực tiếp:
# NoLi Handmade HN
# 🏡 Số 41 ngõ 63 Phố Ô Đồng Lầm, đường ven Hồ Ba Mẫu, Phương Liên, Đống Đa, Hà Nội
# ⏰ Mở cửa: từ 8h-21h. Từ T2 đến Chủ nhật
# ☎️ Hotline: ********** (zalo)
# 🧶 Chỉ đường tới cửa hàng NoLi: https://maps.app.goo.gl/HmtRz7XXnzVCeP6KA

# - Mua trực tiếp:
# NoLi Handmade HCM
# 🏡Số M4-21 đường M4, khu Manhattan Glory, Vinhomes Grandpark, Long Bình, Thủ Đức
# ⏰Mở cửa: 9h-17h. Từ T2-T7
# ☎️ Hotline: ********** (zalo)
# 🧶 Chỉ đường tới: https://maps.app.goo.gl/aKPmoNWzfXsZ8ddN7

# - STK:
# Chủ tài khoản: Mai Khánh Linh
# *MBBank: **********
# * Momo: **********

# ❤️ Học đan móc online dễ dàng cùng NoLi:
# Website: https://noli.vn
# Youtube: https://www.youtube.com/@NoLiHandmadeChannel
# Group: https://www.facebook.com/groups/lopmocbalolennhung

# - Danh sách sản phẩm và mô tả sản phẩm: https://docs.google.com/spreadsheets/d/1JR4UOXeCUG7I2DVscu-YOtgLGEc9qpSl/edit?usp=sharing&ouid=114889868491849224087&rtpof=true&sd=true
# - Video hướng dẫn: https://youtu.be/zjVD1kbNo2M?si=Qn7lzj4g9FhjjECW
# Bật chế độ hỗ trợ trình đọc màn hình
# Để bật chế độ hỗ trợ đọc màn hình, nhấn Ctrl+Alt+Z Để tìm hiểu thêm về các phím tắt, nhấn Ctrl+dấu gạch chéo
# """
#     metadata = {"type": "text", "knowledge_id": knowledge_id, "source": "DIRECT_TEXT"}

#     try:
#         # Test adding new knowledge document
#         logger.info("Testing add new knowledge document...")
#         embedding_knowledge = await embedding_service.add_or_update_knowledge_document(
#             knowledge_id=knowledge_id,
#             content=content,
#             metadata=metadata,
#             source="DIRECT_TEXT",
#         )
#         return embedding_knowledge
#     except Exception as e:
#         logger.error(f"Error during test: {str(e)}")
#         raise


async def get_knowledge_tools(staff: VirtualStaff) -> List[BaseTool]:
    """Get knowledge tools based on staff configuration."""
    knowledge_tools: List[BaseTool] = []
    logger.info(staff.get("configuration").get("knowledge_base").get("knowledge_list"))
    # Check if knowledge base is enabled
    if not staff["configuration"].get("knowledge_base", {}).get("enabled"):
        logger.info(f"Knowledge base disabled for staff {staff['name']}")
        return knowledge_tools
    # Initialize knowledge service
    knowledge_service = KnowledgeService()
    # Get knowledge IDs from configuration
    knowledge_ids = (
        staff["configuration"].get("knowledge_base", {}).get("knowledge_ids", [])
    )
    if not knowledge_ids:
        return []

    for knowledge_id in knowledge_ids:
        # Get retriever for this knowledge
        # await test_add_or_update_knowledge(knowledge_id)
        retriever = await knowledge_service.retriever(knowledge_id)
        if retriever:
            try:
                # Get the first document to extract summary using ainvoke
                docs = await retriever.ainvoke("")
                direction = docs[0].metadata.get("direction") if docs else None

                # Use summary from retriever if available, otherwise use a default description
                description = direction or f"Knowledge base for {knowledge_id}"

                knowledge_tool = create_retriever_tool(
                    name=f"knowledge_{knowledge_id.replace('-', '_')}",
                    retriever=retriever,
                    description=description,
                )
                knowledge_tools.append(knowledge_tool)
                logger.debug(f"Added knowledge tool for ID: {knowledge_id}")
            except Exception as e:
                logger.error(
                    f"Error creating knowledge tool for ID {knowledge_id}: {str(e)}"
                )
                continue
        else:
            logger.warning(
                f"Could not create retriever for knowledge ID: {knowledge_id}"
            )

    logger.info(
        f"Loaded {len(knowledge_tools)} knowledge tools for staff {staff['name']}"
    )
    return knowledge_tools
