flowchart TD
    Start(["Input: User Request"]) --> GetType["Get Request Type"]
    GetType --> IsTask@{ label: "Type is 'task'?" }
    IsTask -- Yes --> ToTask["Route to Task Node<br>Return: [Task, Params]"]
    IsTask -- No --> GatherDesc["Gather All API & Task Descriptions"]
    GatherDesc --> LLM["Use LLM to Analyze User Query"]
    LLM --> LLMTask{"LLM: Matches Task?"}
    LLMTask -- Yes --> ToTask2["Route to Task Node<br>Return: [Task, Params]"]
    LLMTask -- No --> LLMAPI{"LLM: Matches API?"}
    LLMAPI -- Yes --> ToApi["Route to API Node<br>Receive: [API, Params] from Classify Node"]
    LLMAPI -- No --> LLMHybrid{"LLM: Multiple APIs/Tasks or Both?"}
    LLMHybrid -- Yes --> ToHybrid["Route to Hybrid Node<br>Return: [API/Task List, Params in Order]"]
    LLMHybrid -- No --> ToMessage["Route to Message Node<br>Return: Message"]

    IsTask@{ shape: diamond}


