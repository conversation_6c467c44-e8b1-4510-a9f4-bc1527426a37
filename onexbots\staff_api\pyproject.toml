[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "staff_api"
version = "0.1.0"
description = "Staff API Microservice"
authors = [
    { name = "ONEXBOTS Team", email = "<EMAIL>" },
]
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "pydantic>=2.5.2",
    "pydantic-settings>=2.1.0",
    "sqlalchemy>=2.0.23",
    "psycopg2-binary>=2.9.9",
    "boto3>=1.29.3",
    "python-jose>=3.3.0",
    "passlib>=1.7.4",
    "python-multipart>=0.0.6",
    "httpx>=0.25.2",
    "asyncpg>=0.27.0",
    "debugpy>=1.8.0",
    "prometheus-client>=0.19.0",
    "langfuse>=2.0.0",
    "langchain>=0.1.0",
    "langchain-core>=0.1.0",
    "langchain-community>=0.0.10",
    "langchain-openai>=0.0.2",
    "langchain-postgres>=0.0.1",
    "langgraph-checkpoint-postgres>=2.0.0",
    "langgraph>=0.0.20",
    "langgraph-supervisor>=0.0.1",
    "python-dotenv>=1.0.0",
    "openai>=1.3.0",
    "tiktoken>=0.5.1",
    "faiss-cpu>=1.7.4",
    "sentence-transformers>=2.2.2",
    "numexpr>=2.8.7",
    "numpy>=1.24.0",
    "requests>=2.31.0",
    "psycopg>=3.2.0",
    "langmem>= 0.0.20",
    "async_lru>=2.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.5.3",
    "ipython>=8.18.1",
    "jupyter>=1.0.0",
    "pre-commit>=3.5.0",
]

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=staff_api --cov-report=term-missing"

[tool.coverage.run]
source = ["staff_api"]
omit = ["tests/*", "docs/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
] 