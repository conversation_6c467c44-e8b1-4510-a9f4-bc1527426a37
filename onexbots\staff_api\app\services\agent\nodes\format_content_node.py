from typing import Dict, Any
from langgraph.graph import MessagesState
from langchain_core.messages import AIMessage
import json
import re
import logging

logger = logging.getLogger(__name__)

def format_content_node(state: MessagesState) -> Dict[str, Any]:
    """
    Format content based on the target platform.
    
    This node applies platform-specific formatting to the response content,
    ensuring optimal presentation for different channels (web, Facebook, Zalo, etc.).
    
    Args:
        state: Current conversation state
        
    Returns:
        Dict with updated state including formatted content
    """
    logger.info("Processing format content node")
    
    messages = state["messages"]
    platform = state.get("platform", "web")
    processing_type = state.get("processing_type", "general")
    
    if not messages:
        logger.warning("No messages to format")
        return {"messages": messages, "platform": platform}
    
    last_message = messages[-1]
    if not hasattr(last_message, 'content'):
        logger.warning("Last message has no content to format")
        return {"messages": messages, "platform": platform}
    
    original_content = last_message.content
    formatted_content = original_content
    
    logger.info(f"Formatting content for platform: {platform}")
    
    try:
        # Apply platform-specific formatting
        if platform == "facebook":
            formatted_content = _format_for_facebook(original_content, processing_type)
        elif platform == "zalo":
            formatted_content = _format_for_zalo(original_content, processing_type)
        elif platform == "telegram":
            formatted_content = _format_for_telegram(original_content, processing_type)
        elif platform == "whatsapp":
            formatted_content = _format_for_whatsapp(original_content, processing_type)
        elif platform == "web":
            formatted_content = _format_for_web(original_content, processing_type)
        else:
            logger.info(f"No specific formatting for platform: {platform}")
            formatted_content = _format_generic(original_content, processing_type)
        
        # Create new message with formatted content
        if isinstance(last_message, AIMessage):
            formatted_message = AIMessage(content=formatted_content)
        else:
            # Preserve original message type but update content
            formatted_message = last_message.__class__(content=formatted_content)
        
        # Update messages list
        updated_messages = messages[:-1] + [formatted_message]
        
        logger.info("Content formatting completed successfully")
        
        return {
            "messages": updated_messages,
            "platform": platform,
            "content_formatted": True,
            "original_length": len(original_content),
            "formatted_length": len(formatted_content)
        }
        
    except Exception as e:
        logger.error(f"Error formatting content: {str(e)}")
        return {
            "messages": messages,
            "platform": platform,
            "content_formatted": False,
            "format_error": str(e)
        }

def _format_for_facebook(content: str, processing_type: str) -> str:
    """Format content for Facebook Messenger."""
    # Facebook prefers shorter messages with emojis
    formatted = f"📱 {content}"
    
    # Limit length for Facebook
    if len(formatted) > 2000:
        formatted = formatted[:1997] + "..."
    
    # Add processing type indicator
    if processing_type == "api_request":
        formatted = f"🔗 {formatted}"
    elif processing_type == "task_execution":
        formatted = f"⚡ {formatted}"
    
    return formatted

def _format_for_zalo(content: str, processing_type: str) -> str:
    """Format content for Zalo."""
    # Zalo supports rich formatting
    formatted = f"💬 {content}"
    
    # Add Vietnamese-friendly formatting
    formatted = formatted.replace("**", "*")  # Zalo uses single asterisk for bold
    
    # Add processing type indicator
    if processing_type == "api_request":
        formatted = f"🌐 {formatted}"
    elif processing_type == "task_execution":
        formatted = f"🎯 {formatted}"
    
    return formatted

def _format_for_telegram(content: str, processing_type: str) -> str:
    """Format content for Telegram."""
    # Telegram supports Markdown
    formatted = content
    
    # Convert to Telegram markdown if needed
    formatted = re.sub(r'\*\*(.*?)\*\*', r'*\1*', formatted)  # Bold
    formatted = re.sub(r'__(.*?)__', r'_\1_', formatted)      # Italic
    
    return formatted

def _format_for_whatsapp(content: str, processing_type: str) -> str:
    """Format content for WhatsApp."""
    # WhatsApp has character limits and prefers simple formatting
    formatted = content
    
    # Convert markdown to WhatsApp format
    formatted = re.sub(r'\*\*(.*?)\*\*', r'*\1*', formatted)  # Bold
    formatted = re.sub(r'__(.*?)__', r'_\1_', formatted)      # Italic
    
    # Add emoji based on processing type
    if processing_type == "api_request":
        formatted = f"🔗 {formatted}"
    elif processing_type == "task_execution":
        formatted = f"✅ {formatted}"
    
    return formatted

def _format_for_web(content: str, processing_type: str) -> str:
    """Format content for web interface."""
    # Web can handle rich HTML/Markdown formatting
    formatted = content
    
    # Enhance with HTML-like formatting if needed
    if processing_type == "api_request":
        formatted = f"<div class='api-response'>🌐 {formatted}</div>"
    elif processing_type == "task_execution":
        formatted = f"<div class='task-result'>⚡ {formatted}</div>"
    
    return formatted

def _format_generic(content: str, processing_type: str) -> str:
    """Generic formatting for unknown platforms."""
    formatted = content
    
    # Add simple indicators
    if processing_type == "api_request":
        formatted = f"[API] {formatted}"
    elif processing_type == "task_execution":
        formatted = f"[TASK] {formatted}"
    
    return formatted
