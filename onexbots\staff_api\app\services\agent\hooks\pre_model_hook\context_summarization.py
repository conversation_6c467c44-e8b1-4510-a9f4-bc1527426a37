from langchain_core.prompts import ChatPromptTemplate
from langmem.short_term import SummarizationNode
from langchain_core.messages.utils import count_tokens_approximately
from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI

# Custom summary prompts to retain user-provided facts
INITIAL_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{messages}"),
        (
            "user",
            "Create a summary of the conversation above. IMPORTANT: Always retain any facts the user provides about themselves (e.g., name, preferences, requirements, etc.). Do NOT include any statements about the AI's inability to answer, lack of information, or apologies. Only summarize factual information and relevant context from the conversation.",
        ),
    ]
)

EXISTING_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{messages}"),
        (
            "user",
            "This is the summary so far: {existing_summary}\n\nExtend this summary by taking into account the new messages above. IMPORTANT: Do not lose any user-provided facts (such as name, preferences, requirements, etc.). Do NOT include any statements about the AI's inability to answer, lack of information, or apologies. Only summarize factual information and relevant context from the conversation.",
        ),
    ]
)

FINAL_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{system_message}"),
        ("system", "Summary of the conversation so far: {summary}"),
        ("placeholder", "{messages}"),
    ]
)

model = ChatOpenAI(model="gpt-4o-mini")
summarization_node = SummarizationNode(
    token_counter=count_tokens_approximately,
    model=model,
    max_tokens=8000,
    max_summary_tokens=700,
    # max_tokens=1000,
    # max_summary_tokens=700,
    output_messages_key="llm_input_messages",
    initial_summary_prompt=INITIAL_SUMMARY_PROMPT,
    existing_summary_prompt=EXISTING_SUMMARY_PROMPT,
    final_prompt=FINAL_SUMMARY_PROMPT,
)


def custom_pre_model_hook(state):
    messages = state["messages"]
    context = state.get("context", {})
    if not messages:
        return state
    last_human_idx = None

    # get last human message
    for i in range(len(messages) - 1, -1, -1):
        if isinstance(messages[i], HumanMessage):
            last_human_idx = i
            break

    if last_human_idx is None:
        summary = summarization_node.invoke(state)
        context = summary.get("context")
        return {**state, "context": context}

    # Only include context in summary_state if it is not None and not empty
    if context:
        summary_state = {"messages": messages[:last_human_idx], "context": context}
    else:
        summary_state = {"messages": messages[:last_human_idx]}
    summary = summarization_node.invoke(summary_state)
    context = summary.get("context")
    remaining_messages = messages[last_human_idx:]
    new_messages = summary.get("llm_input_messages", []) + remaining_messages
    # Only include context if it is not None and not empty
    new_state = {**state, "llm_input_messages": new_messages}
    if context:
        new_state["context"] = context
    elif "context" in new_state:
        del new_state["context"]
    return new_state
