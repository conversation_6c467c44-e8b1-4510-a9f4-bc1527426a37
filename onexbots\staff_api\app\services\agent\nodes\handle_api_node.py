from typing import Dict, Any
from langgraph.graph import MessagesState
import logging

logger = logging.getLogger(__name__)

def handle_api_node(state: MessagesState) -> Dict[str, Any]:
    """
    Handle API-related requests.
    
    This node processes requests that involve API calls, data retrieval,
    or external service interactions. It prepares the state for tool
    execution with API-specific context.
    
    Args:
        state: Current conversation state
        
    Returns:
        Dict with updated state directing to tool execution
    """
    logger.info("Processing API node - preparing for API tool execution")
    
    messages = state["messages"]
    
    # Extract API-related context from the message
    last_message = messages[-1] if messages else None
    api_context = {}
    
    if last_message and hasattr(last_message, 'content'):
        content = last_message.content.lower()
        
        # Extract potential API parameters or endpoints
        if "endpoint" in content:
            api_context["has_endpoint"] = True
        if "data" in content or "fetch" in content:
            api_context["operation_type"] = "fetch"
        if "post" in content or "create" in content:
            api_context["operation_type"] = "create"
        if "update" in content or "modify" in content:
            api_context["operation_type"] = "update"
        if "delete" in content or "remove" in content:
            api_context["operation_type"] = "delete"
    
    return {
        "messages": messages,
        "next_action": "tool",
        "tool_type": "api",
        "requires_tools": True,
        "api_context": api_context,
        "processing_type": "api_request"
    }
