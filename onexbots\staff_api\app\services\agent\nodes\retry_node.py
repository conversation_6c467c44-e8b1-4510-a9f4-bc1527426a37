from typing import Dict, Any
from langgraph.graph import MessagesState
import logging
import time

logger = logging.getLogger(__name__)

def retry_node(state: MessagesState) -> Dict[str, Any]:
    """
    Handle retry logic and reset state for retry.
    
    This node manages the retry mechanism when errors occur during
    workflow execution. It implements exponential backoff and
    determines when to stop retrying.
    
    Args:
        state: Current conversation state
        
    Returns:
        Dict with updated state for retry or termination
    """
    logger.info("Processing retry node")
    
    retry_count = state.get("retry_count", 0)
    max_retries = state.get("max_retries", 3)
    error_message = state.get("error_message", "Unknown error")
    error_type = state.get("error_type", "general")
    
    logger.info(f"Retry attempt {retry_count}/{max_retries} for error: {error_type}")
    
    if retry_count >= max_retries:
        logger.error(f"Max retries ({max_retries}) reached, terminating")
        
        # Create final error state
        return {
            "messages": state["messages"],
            "has_error": True,
            "error_message": f"Maximum retry attempts ({max_retries}) exceeded. Last error: {error_message}",
            "retry_count": retry_count,
            "max_retries_reached": True,
            "should_retry": False,
            "final_state": "error_terminated"
        }
    
    # Calculate backoff delay based on retry count
    backoff_delay = _calculate_backoff_delay(retry_count, error_type)
    
    logger.info(f"Implementing {backoff_delay}s backoff before retry")
    
    # In a real implementation, you might want to implement async sleep
    # For now, we'll just log the intended delay
    time.sleep(min(backoff_delay, 5))  # Cap at 5 seconds for responsiveness
    
    # Reset error state for retry but preserve retry count
    reset_state = {
        "messages": state["messages"],
        "has_error": False,
        "error_message": None,
        "retry_count": retry_count,  # Keep current count, will be incremented by error handler
        "max_retries_reached": False,
        "should_retry": True,
        "task_type": None,  # Reset to trigger re-classification
        "next_action": None,  # Reset action
        "tool_type": None,  # Reset tool type
        "backoff_delay": backoff_delay,
        "retry_reason": error_type,
        "original_error": error_message
    }
    
    # Preserve important context that shouldn't be reset
    context_keys = [
        "platform", "processing_type", "api_context", 
        "task_context", "hybrid_context", "classification_confidence"
    ]
    
    for key in context_keys:
        if key in state:
            reset_state[key] = state[key]
    
    logger.info(f"State reset for retry, preserving context: {list(reset_state.keys())}")
    
    return reset_state

def _calculate_backoff_delay(retry_count: int, error_type: str) -> float:
    """
    Calculate exponential backoff delay based on retry count and error type.
    
    Args:
        retry_count: Current retry attempt number
        error_type: Type of error that occurred
        
    Returns:
        Delay in seconds
    """
    base_delay = 1.0  # Base delay in seconds
    
    # Adjust base delay based on error type
    error_multipliers = {
        "timeout": 2.0,      # Longer delays for timeouts
        "connection": 1.5,   # Medium delays for connection issues
        "permission": 0.5,   # Shorter delays for permission issues (likely won't resolve)
        "not_found": 0.5,    # Shorter delays for not found errors
        "general": 1.0       # Standard delay for general errors
    }
    
    multiplier = error_multipliers.get(error_type, 1.0)
    
    # Exponential backoff: base_delay * multiplier * (2 ^ retry_count)
    delay = base_delay * multiplier * (2 ** retry_count)
    
    # Cap the maximum delay at 30 seconds
    max_delay = 30.0
    
    return min(delay, max_delay)
