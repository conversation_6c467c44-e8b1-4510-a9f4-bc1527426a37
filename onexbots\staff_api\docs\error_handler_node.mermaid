flowchart TD
    Start(["Input: Error Handler Node<br/>Receive: Error Details from Previous Node"])
    LogError["Log/Report Error Details"]
    DetectType["Detect Error Type (timeout, connection, permission, not_found, general)"]
    CheckRetry["Check Retry Count vs Max Retries"]
    Retryable{"Retryable Error Type?"}
    CanRetry{"Retries Left?"}
    Retry(["Trigger Retry Logic (Increment Count)"])
    ReturnToProcess["Return to Previous Processing Node<br/>(message, hybrid, task, API, etc.)"]
    NotRetryable["Non-Retryable or Max Retries Reached"]
    NeedsFormat["Needs Output Formatting? (Platform/Content)"]
    Format(["Format Error Output for User"])
    EndNode(["End Workflow (Return Error to User)"])

    Start --> LogError --> DetectType --> Retryable
    Retryable -- Yes --> CheckRetry
    Retryable -- No --> NotRetryable
    CheckRetry --> CanRetry
    CanRetry -- Yes --> Retry --> ReturnToProcess
    CanRetry -- No --> NotRetryable
    NotRetryable --> NeedsFormat
    NeedsFormat -- Yes --> Format --> EndNode
    NeedsFormat -- No --> EndNode 