from typing import Dict, Any
from langgraph.graph import MessagesState
import logging

logger = logging.getLogger(__name__)

def error_handler_node(state: MessagesState) -> Dict[str, Any]:
    """
    Handle errors and determine next action.
    
    This node processes errors that occur during workflow execution
    and determines whether to retry, format content, or end the process.
    It also handles platform-specific formatting requirements.
    
    Args:
        state: Current conversation state
        
    Returns:
        Dict with updated state and error handling decisions
    """
    logger.info("Processing error handler node")
    
    has_error = state.get("has_error", False)
    retry_count = state.get("retry_count", 0)
    max_retries = state.get("max_retries", 3)
    
    if has_error:
        error_message = state.get("error_message", "Unknown error")
        logger.warning(f"Error detected (attempt {retry_count + 1}): {error_message}")
        
        # Analyze error type for better handling
        error_type = "general"
        if "timeout" in error_message.lower():
            error_type = "timeout"
        elif "connection" in error_message.lower():
            error_type = "connection"
        elif "permission" in error_message.lower():
            error_type = "permission"
        elif "not found" in error_message.lower():
            error_type = "not_found"
        
        # Determine if retry is appropriate
        should_retry = (
            retry_count < max_retries and 
            error_type in ["timeout", "connection", "general"]
        )
        
        if should_retry:
            logger.info(f"Preparing for retry (attempt {retry_count + 1}/{max_retries})")
            return {
                "messages": state["messages"],
                "has_error": True,
                "error_message": error_message,
                "error_type": error_type,
                "retry_count": retry_count + 1,
                "format_required": False,
                "should_retry": True,
                "max_retries_reached": False
            }
        else:
            logger.error(f"Max retries reached or non-retryable error: {error_type}")
            return {
                "messages": state["messages"],
                "has_error": True,
                "error_message": error_message,
                "error_type": error_type,
                "retry_count": retry_count,
                "format_required": False,
                "should_retry": False,
                "max_retries_reached": True
            }
    else:
        logger.info("No errors detected, checking formatting requirements")
        
        # Check if formatting is needed based on platform
        platform = state.get("platform", "web")
        processing_type = state.get("processing_type", "general")
        
        # Determine if formatting is required
        format_required = False
        
        # Platform-specific formatting requirements
        if platform in ["facebook", "zalo", "telegram", "whatsapp"]:
            format_required = True
        
        # Processing type specific formatting
        if processing_type in ["api_request", "task_execution"]:
            format_required = True
        
        # Check if response contains structured data that needs formatting
        messages = state.get("messages", [])
        if messages:
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                content = last_message.content
                # Check for JSON, XML, or other structured content
                if any(marker in content for marker in ["{", "[", "<", "```"]):
                    format_required = True
        
        logger.info(f"Formatting required: {format_required} for platform: {platform}")
        
        return {
            "messages": state["messages"],
            "has_error": False,
            "retry_count": retry_count,
            "format_required": format_required,
            "platform": platform,
            "processing_type": processing_type,
            "should_retry": False,
            "max_retries_reached": False
        }
