from typing import Dict, Any
from langgraph.graph import MessagesState
import logging

logger = logging.getLogger(__name__)

# Task classification types
TASK_TYPES = {
    "MESSAGE": "message",
    "API": "api", 
    "TASK": "task",
    "HYBRID": "hybrid"
}

def classify_task_node(state: MessagesState) -> Dict[str, Any]:
    """
    Classify the incoming message to determine the appropriate handling path.
    
    This node analyzes the user's message and determines which type of processing
    is needed based on keywords and content analysis.
    
    Args:
        state: Current conversation state containing messages
        
    Returns:
        Dict containing task_type and updated state
    """
    messages = state["messages"]
    if not messages:
        logger.info("No messages found, defaulting to MESSAGE type")
        return {"task_type": TASK_TYPES["MESSAGE"], "messages": messages}
    
    last_message = messages[-1]
    message_content = (
        last_message.content.lower() if hasattr(last_message, "content") else ""
    )
    
    logger.debug(f"Classifying message: {message_content[:100]}...")
    
    # Enhanced classification logic - can be replaced with ML models
    api_keywords = ["api", "call", "request", "endpoint", "fetch", "get data", "retrieve"]
    task_keywords = ["task", "do", "execute", "perform", "action", "run", "process"]
    hybrid_keywords = ["both", "and", "also", "plus", "combine", "together"]
    
    if any(keyword in message_content for keyword in api_keywords):
        task_type = TASK_TYPES["API"]
        logger.info("Classified as API task")
    elif any(keyword in message_content for keyword in task_keywords):
        task_type = TASK_TYPES["TASK"]
        logger.info("Classified as TASK")
    elif any(keyword in message_content for keyword in hybrid_keywords):
        task_type = TASK_TYPES["HYBRID"]
        logger.info("Classified as HYBRID task")
    else:
        task_type = TASK_TYPES["MESSAGE"]
        logger.info("Classified as MESSAGE (default)")
    
    return {
        "task_type": task_type, 
        "messages": messages,
        "classification_confidence": 0.8  # Can be enhanced with ML confidence scores
    }
