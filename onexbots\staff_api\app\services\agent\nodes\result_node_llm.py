from typing import Dict, Any
from langgraph.graph import MessagesState
from langchain_core.messages import SystemMessage, AIMessage
import logging

logger = logging.getLogger(__name__)

def create_result_node_llm(model, system_prompt):
    """
    Create a result generation node with the provided model and prompt.
    
    This factory function creates a result node that generates LLM responses
    based on the current conversation state and context.
    
    Args:
        model: The LLM model to use for generation
        system_prompt: The system prompt for the model
        
    Returns:
        Result node function
    """
    
    def result_node_llm(state: MessagesState) -> Dict[str, Any]:
        """
        Generate LLM response based on the current state.
        
        This node creates the final response using the LLM, incorporating
        all previous context, tool results, and conversation history.
        
        Args:
            state: Current conversation state
            
        Returns:
            Dict with updated state including LLM response
        """
        logger.info("Generating LLM result")
        
        try:
            messages = state["messages"]
            processing_type = state.get("processing_type", "general")
            
            # Prepare the prompt based on processing type
            if isinstance(system_prompt, str):
                system_message = SystemMessage(content=system_prompt)
            else:
                system_message = system_prompt
            
            # Create context-aware system message based on processing type
            context_additions = []
            
            if processing_type == "api_request":
                api_context = state.get("api_context", {})
                if api_context:
                    context_additions.append(f"API Context: {api_context}")
            
            elif processing_type == "task_execution":
                task_context = state.get("task_context", {})
                if task_context:
                    context_additions.append(f"Task Context: {task_context}")
            
            elif processing_type == "hybrid_execution":
                hybrid_context = state.get("hybrid_context", {})
                if hybrid_context:
                    context_additions.append(f"Hybrid Context: {hybrid_context}")
            
            # Add context to system message if needed
            if context_additions:
                enhanced_system_content = system_message.content + "\n\nAdditional Context:\n" + "\n".join(context_additions)
                system_message = SystemMessage(content=enhanced_system_content)
            
            # Prepare messages for the model
            prompt_messages = [system_message] + messages
            
            # Generate response using the LLM
            logger.debug("Invoking LLM model for response generation")
            response = model.invoke(prompt_messages)
            
            # Ensure response is an AIMessage
            if not isinstance(response, AIMessage):
                response = AIMessage(content=str(response))
            
            # Add the response to messages
            updated_messages = messages + [response]
            
            logger.info("LLM response generated successfully")
            
            return {
                "messages": updated_messages,
                "has_error": False,
                "retry_count": state.get("retry_count", 0),
                "response_generated": True,
                "processing_complete": True
            }
            
        except Exception as e:
            logger.error(f"Error in result_node_llm: {str(e)}")
            
            # Create error response
            error_response = AIMessage(
                content=f"I apologize, but I encountered an error while processing your request: {str(e)}"
            )
            
            return {
                "messages": messages + [error_response],
                "has_error": True,
                "error_message": str(e),
                "retry_count": state.get("retry_count", 0),
                "response_generated": True,
                "processing_complete": False
            }
    
    return result_node_llm
