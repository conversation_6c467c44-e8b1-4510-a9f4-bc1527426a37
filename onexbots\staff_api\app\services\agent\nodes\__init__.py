"""
Node registry for the AI workflow system.

This module provides easy access to all workflow nodes and their factory functions.
Each node represents a specific step in the AI workflow as defined in ai_workflow.mermaid.
"""

from .classify_task_node import classify_task_node, TASK_TYPES
from .message_node import message_node
from .handle_api_node import handle_api_node
from .task_node import task_node
from .hybrid_node import hybrid_node
from .tool_node import create_tool_node
from .result_node_llm import create_result_node_llm
from .error_handler_node import error_handler_node
from .format_content_node import format_content_node
from .retry_node import retry_node

__all__ = [
    # Core workflow nodes
    "classify_task_node",
    "message_node", 
    "handle_api_node",
    "task_node",
    "hybrid_node",
    "error_handler_node",
    "format_content_node",
    "retry_node",
    
    # Factory functions for configurable nodes
    "create_tool_node",
    "create_result_node_llm",
    
    # Constants
    "TASK_TYPES",
]

# Node metadata for workflow construction
NODE_METADATA = {
    "classify_task": {
        "function": classify_task_node,
        "type": "classifier",
        "description": "Classifies incoming messages to determine processing path",
        "outputs": ["message", "api", "task", "hybrid"]
    },
    "message": {
        "function": message_node,
        "type": "processor", 
        "description": "Handles simple conversational messages",
        "outputs": ["result"]
    },
    "handle_api": {
        "function": handle_api_node,
        "type": "processor",
        "description": "Processes API-related requests",
        "outputs": ["tool"]
    },
    "task": {
        "function": task_node,
        "type": "processor",
        "description": "Handles task execution requests", 
        "outputs": ["tool"]
    },
    "hybrid": {
        "function": hybrid_node,
        "type": "processor",
        "description": "Processes hybrid requests requiring multiple capabilities",
        "outputs": ["tool"]
    },
    "tool": {
        "function": None,  # Created via factory
        "type": "executor",
        "description": "Executes tools based on request type",
        "outputs": ["result", "tool"]  # Can loop back to itself
    },
    "result_llm": {
        "function": None,  # Created via factory
        "type": "generator",
        "description": "Generates final LLM response",
        "outputs": ["error_handler"]
    },
    "error_handler": {
        "function": error_handler_node,
        "type": "handler",
        "description": "Handles errors and determines retry/format actions",
        "outputs": ["retry", "format_content", "end"]
    },
    "format_content": {
        "function": format_content_node,
        "type": "formatter",
        "description": "Formats content for specific platforms",
        "outputs": ["end"]
    },
    "retry": {
        "function": retry_node,
        "type": "controller",
        "description": "Manages retry logic with exponential backoff",
        "outputs": ["classify_task", "end"]
    }
}

def get_node_function(node_name: str):
    """
    Get the function for a specific node.
    
    Args:
        node_name: Name of the node
        
    Returns:
        Node function or None if not found
    """
    metadata = NODE_METADATA.get(node_name)
    if metadata:
        return metadata["function"]
    return None

def get_node_metadata(node_name: str):
    """
    Get metadata for a specific node.
    
    Args:
        node_name: Name of the node
        
    Returns:
        Node metadata dict or None if not found
    """
    return NODE_METADATA.get(node_name)

def list_available_nodes():
    """
    List all available nodes.
    
    Returns:
        List of node names
    """
    return list(NODE_METADATA.keys())
