from typing import Dict, Any
from langgraph.graph import MessagesState
import logging

logger = logging.getLogger(__name__)

def hybrid_node(state: MessagesState) -> Dict[str, Any]:
    """
    Handle hybrid requests that need both message and tool responses.
    
    This node processes complex requests that require both conversational
    responses and tool execution. It combines API calls, task execution,
    and conversational elements.
    
    Args:
        state: Current conversation state
        
    Returns:
        Dict with updated state directing to tool execution with hybrid context
    """
    logger.info("Processing hybrid node - preparing for combined tool and message execution")
    
    messages = state["messages"]
    
    # Extract hybrid context from the message
    last_message = messages[-1] if messages else None
    hybrid_context = {
        "requires_api": False,
        "requires_task": False,
        "requires_conversation": True,  # Always true for hybrid
        "execution_order": []
    }
    
    if last_message and hasattr(last_message, 'content'):
        content = last_message.content.lower()
        
        # Check for API requirements
        api_keywords = ["api", "call", "request", "endpoint", "fetch", "get data"]
        if any(keyword in content for keyword in api_keywords):
            hybrid_context["requires_api"] = True
            hybrid_context["execution_order"].append("api")
        
        # Check for task requirements
        task_keywords = ["task", "do", "execute", "perform", "action", "run"]
        if any(keyword in content for keyword in task_keywords):
            hybrid_context["requires_task"] = True
            hybrid_context["execution_order"].append("task")
        
        # Determine execution strategy
        if "first" in content or "before" in content:
            hybrid_context["execution_strategy"] = "sequential"
        elif "together" in content or "simultaneously" in content:
            hybrid_context["execution_strategy"] = "parallel"
        else:
            hybrid_context["execution_strategy"] = "adaptive"
        
        # Check for response formatting requirements
        if any(word in content for word in ["explain", "describe", "tell me"]):
            hybrid_context["response_style"] = "explanatory"
        elif any(word in content for word in ["summary", "brief", "quick"]):
            hybrid_context["response_style"] = "concise"
        else:
            hybrid_context["response_style"] = "detailed"
    
    return {
        "messages": messages,
        "next_action": "tool",
        "tool_type": "hybrid",
        "requires_tools": True,
        "hybrid_context": hybrid_context,
        "processing_type": "hybrid_execution"
    }
